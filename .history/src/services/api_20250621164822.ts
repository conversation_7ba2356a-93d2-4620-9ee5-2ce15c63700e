// 动态获取API基础URL
const getApiBaseUrl = () => {
  // 使用vite.config.ts中定义的全局变量
  return (globalThis as any).__API_BASE_URL__ || 'http://114.132.154.140:5000/api';
};

const API_BASE_URL = getApiBaseUrl();

// Token管理
class TokenManager {
  private static TOKEN_KEY = 'auth_token';
  private static USER_KEY = 'user_info';
  private static REMEMBER_KEY = 'remember_credentials';

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static removeToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  static getUser(): any | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  static setUser(user: any): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  // 记住账号相关方法
  static saveCredentials(username: string, password: string): void {
    const credentials = {
      username,
      password,
      savedAt: new Date().toISOString()
    };
    localStorage.setItem(this.REMEMBER_KEY, JSON.stringify(credentials));
  }

  static getSavedCredentials(): { username: string; password: string } | null {
    try {
      const credentialsStr = localStorage.getItem(this.REMEMBER_KEY);
      if (credentialsStr) {
        const credentials = JSON.parse(credentialsStr);
        return {
          username: credentials.username || '',
          password: credentials.password || ''
        };
      }
    } catch (error) {
      console.error('读取保存的账号信息失败:', error);
    }
    return null;
  }

  static clearSavedCredentials(): void {
    localStorage.removeItem(this.REMEMBER_KEY);
  }

  static hasSavedCredentials(): boolean {
    return localStorage.getItem(this.REMEMBER_KEY) !== null;
  }
}

// 通用请求函数
async function apiRequest(url: string, options: RequestInit = {}) {
  try {
    const token = TokenManager.getToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // 添加原有的headers
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    // 添加认证头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${API_BASE_URL}${url}`, {
      headers,
      ...options,
    });

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData.error) {
          errorMessage = errorData.error;
        }
      } catch (parseError) {
        // 如果解析失败，使用默认错误消息
      }
      
      // 如果是401错误，清除token（但不在登录页面时跳转）
      if (response.status === 401) {
        TokenManager.removeToken();
        // 只有当不是登录API时才跳转
        if (!url.includes('/auth/login') && window.location.pathname !== '/login') {
          window.location.href = '/login';
          return;
        }
      }
      
      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || '请求失败');
    }

    return data.data;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
}

// 患者管理API
export const personnelAPI = {
  // 获取患者列表
  getList: async (params: {
    search?: string;
    page?: number;
    limit?: number;
  } = {}) => {
    const searchParams = new URLSearchParams();
    if (params.search) searchParams.append('search', params.search);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    
    const queryString = searchParams.toString();
    const url = `/personnel${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest(url);
  },

  // 获取患者详情
  getDetail: async (id: string) => {
    return apiRequest(`/personnel/${id}`);
  },

  // 添加患者
  add: async (data: {
    name: string;
    phone: string;
    age: number;
    gender: string;
    enrollment_date?: string;
    training_status?: string;
    patient_id: string;
    exercise_plan_id?: string;
  }) => {
    return apiRequest('/personnel', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 更新患者
  update: async (id: string, data: {
    name?: string;
    phone?: string;
    age?: number;
    gender?: string;
    enrollment_date?: string;
    training_status?: string;
    patient_id?: string;
    exercise_plan_id?: string;
  }) => {
    return apiRequest(`/personnel/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // 删除患者
  delete: async (id: string) => {
    return apiRequest(`/personnel/${id}`, {
      method: 'DELETE',
    });
  },

  // 批量删除患者
  batchDelete: async (ids: string[]) => {
    return apiRequest('/personnel/batch', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    });
  },

  // 批量更新患者训练状态
  batchUpdateStatus: async (ids: string[], training_status: string) => {
    return apiRequest('/personnel/batch', {
      method: 'PUT',
      body: JSON.stringify({ ids, training_status }),
    });
  },

  // 获取可用的锻炼计划列表
  getAvailableExercisePlans: async () => {
    return apiRequest('/personnel/exercise-plans');
  },
};

// 通话记录API
export const callRecordsAPI = {
  // 获取通话记录列表
  getList: async (params: {
    search?: string;
    start_date?: string;
    end_date?: string;
    patient_ids?: string;
    doctor_intervention?: string;
    page?: number;
    limit?: number;
  } = {}) => {
    try {
      const token = TokenManager.getToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value) searchParams.append(key, value.toString());
      });
      
      const queryString = searchParams.toString();
      const url = `/call-records${queryString ? `?${queryString}` : ''}`;
      
      console.log('完整API请求URL:', `${API_BASE_URL}${url}`);
      
      const response = await fetch(`${API_BASE_URL}${url}`, {
        headers,
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          // 如果解析失败，使用默认错误消息
        }
        
        if (response.status === 401) {
          TokenManager.removeToken();
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
            return;
          }
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('完整API响应:', data);
      
      if (!data.success) {
        throw new Error(data.error || '请求失败');
      }

      // 对于通话记录API，返回完整的响应数据而不仅仅是data字段
      return {
        data: data.data,
        total: data.total,
        page: data.page,
        limit: data.limit,
        success: data.success
      };
    } catch (error) {
      console.error('通话记录API请求错误:', error);
      throw error;
    }
  },

  // 发起即时通话
  makeImmediateCall: async (phoneNumber: string, objId?: string) => {
    return apiRequest('/call-records/immediate-call', {
      method: 'POST',
      body: JSON.stringify({ 
        phone_number: phoneNumber,
        obj_id: objId 
      }),
    });
  },

  // 根据obj_id获取通话状态
  getCallStatus: async (objId: string) => {
    return apiRequest(`/call-records/status/${objId}`);
  },

  // 根据obj_id获取通话详情
  getCallDetail: async (objId: string) => {
    return apiRequest(`/call-records/detail/${objId}`);
  },

  // 删除单个通话记录
  delete: async (id: string) => {
    try {
      const token = TokenManager.getToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/call-records/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          // 如果解析失败，使用默认错误消息
        }
        
        if (response.status === 401) {
          TokenManager.removeToken();
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
            return;
          }
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('单个删除API响应:', data);
      
      if (!data.success) {
        throw new Error(data.error || '删除失败');
      }

      return data;
    } catch (error) {
      console.error('单个删除API请求错误:', error);
      throw error;
    }
  },

  // 批量删除通话记录（直接调用后端批量删除API）
  batchDelete: async (ids: string[]) => {
    try {
      const token = TokenManager.getToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/call-records/batch`, {
        method: 'DELETE',
        headers,
        body: JSON.stringify({ ids }),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          // 如果解析失败，使用默认错误消息
        }
        
        if (response.status === 401) {
          TokenManager.removeToken();
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
            return;
          }
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('批量删除API响应:', data);
      
      if (!data.success) {
        throw new Error(data.error || '删除失败');
      }

      // 返回完整的响应数据
      return {
        deleted_count: data.deleted_count || 0,
        not_found_count: data.not_found_count || 0,
        message: data.message,
        deleted_ids: data.deleted_ids || []
      };
    } catch (error) {
      console.error('批量删除API请求错误:', error);
      throw error;
    }
  },
};

// 统计数据API
export const statsAPI = {
  // 获取统计数据
  get: async () => {
    return apiRequest('/stats');
  },
};

// 康复进度API
export const progressAPI = {
  // 获取康复进度数据
  get: async (params: { search?: string } = {}) => {
    const searchParams = new URLSearchParams();
    if (params.search) searchParams.append('search', params.search);
    
    const queryString = searchParams.toString();
    const url = `/progress${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest(url);
  },
};

// 智能摘要API
export const insightsAPI = {
  // 获取智能摘要数据
  get: async (params: { time_range?: string } = {}) => {
    const searchParams = new URLSearchParams();
    if (params.time_range) searchParams.append('time_range', params.time_range);
    
    const queryString = searchParams.toString();
    const url = `/insights${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest(url);
  },
};

// 健康检查API
export const healthAPI = {
  check: async () => {
    return apiRequest('/health');
  },
};

// 认证相关API
export const authAPI = {
  // 用户登录
  login: async (credentials: { username: string; password: string }) => {
    try {
      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });
      
      // 检查响应是否有效
      if (!response) {
        throw new Error('服务器响应无效');
      }
      
      // 保存token和用户信息
      if (response.token) {
        TokenManager.setToken(response.token);
        TokenManager.setUser(response.user);
      } else {
        throw new Error('登录响应中缺少token');
      }
      
      return response;
    } catch (error: any) {
      // 处理网络错误
      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        throw new Error('无法连接到服务器，请检查后端服务是否启动');
      }
      throw error;
    }
  },

  // 验证token
  verify: async () => {
    return apiRequest('/auth/verify');
  },

  // 登出
  logout: async () => {
    try {
      await apiRequest('/auth/logout', { method: 'POST' });
    } catch (error) {
      // 即使后端登出失败，也要清除本地token
      console.warn('后端登出失败:', error);
    } finally {
      TokenManager.removeToken();
    }
  },
};

// 用户管理API（仅管理员可访问）
export const usersAPI = {
  // 获取医生用户列表
  getList: async () => {
    return apiRequest('/users');
  },

  // 创建新医生用户
  create: async (userData: {
    username: string;
    password: string;
    full_name: string;
    email: string;
    phone?: string;
  }) => {
    return apiRequest('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // 更新医生用户信息
  update: async (userId: string, userData: {
    username?: string;
    password?: string;
    full_name?: string;
    email?: string;
    phone?: string;
    is_active?: boolean;
  }) => {
    return apiRequest(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  // 删除（停用）医生用户
  delete: async (userId: string) => {
    return apiRequest(`/users/${userId}`, {
      method: 'DELETE',
    });
  },
};

// 随访统计API
export const followUpStatsAPI = {
  // 获取历史随访统计数据
  getStats: async (params: {
    start_date?: string;
    end_date?: string;
    time_range?: string;
    page?: number;
    limit?: number;
  } = {}) => {
    try {
      const token = TokenManager.getToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const searchParams = new URLSearchParams();
      if (params.start_date) searchParams.append('start_date', params.start_date);
      if (params.end_date) searchParams.append('end_date', params.end_date);
      if (params.time_range) searchParams.append('time_range', params.time_range);
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      
      const queryString = searchParams.toString();
      const url = `/follow-up-stats${queryString ? `?${queryString}` : ''}`;
      
      console.log('随访统计API请求URL:', `${API_BASE_URL}${url}`);
      
      const response = await fetch(`${API_BASE_URL}${url}`, {
        headers,
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          // 如果解析失败，使用默认错误消息
        }
        
        if (response.status === 401) {
          TokenManager.removeToken();
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
            return;
          }
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('随访统计API完整响应:', data);
      
      if (!data.success) {
        throw new Error(data.error || '请求失败');
      }

      // 返回完整的响应数据
      return {
        data: data.data,
        total: data.total,
        page: data.page,
        limit: data.limit,
        time_range: data.time_range,
        success: data.success
      };
    } catch (error) {
      console.error('随访统计API请求错误:', error);
      throw error;
    }
  },
};

// 锻炼计划API
export const exercisePlanAPI = {
  // 获取锻炼计划列表
  getList: async (params: {
    page?: number;
    page_size?: number;
    patient_id?: string;
    status?: string;
    doctor_id?: string;
  } = {}) => {
    try {
      const token = TokenManager.getToken();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value) searchParams.append(key, value.toString());
      });

      const queryString = searchParams.toString();
      const url = `/exercise-plans${queryString ? `?${queryString}` : ''}`;

      console.log('锻炼计划API请求URL:', `${API_BASE_URL}${url}`);

      const response = await fetch(`${API_BASE_URL}${url}`, {
        headers,
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          // 如果解析失败，使用默认错误消息
        }

        if (response.status === 401) {
          TokenManager.removeToken();
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
            return;
          }
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('锻炼计划API完整响应:', data);

      if (!data.success) {
        throw new Error(data.error || '请求失败');
      }

      // 返回完整的响应数据
      return {
        data: data.data,
        pagination: data.pagination,
        success: data.success
      };
    } catch (error) {
      console.error('锻炼计划API请求错误:', error);
      throw error;
    }
  },

  // 获取锻炼计划详情
  getDetail: async (planId: string) => {
    return apiRequest(`/exercise-plans/${planId}`);
  },

  // 创建锻炼计划
  create: async (data: {
    patient_id: string;
    plan_name: string;
    description?: string;
    total_weeks: number;
    initial_daily_count: number;
    weekly_increment: number;
    max_rest_days_per_week: number;
    start_date: string;
  }) => {
    return apiRequest('/exercise-plans', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 更新锻炼计划
  update: async (planId: string, data: {
    plan_name?: string;
    description?: string;
    status?: string;
  }) => {
    return apiRequest(`/exercise-plans/${planId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // 删除锻炼计划（软删除）
  delete: async (planId: string) => {
    return apiRequest(`/exercise-plans/${planId}`, {
      method: 'DELETE',
    });
  },
};

// 导出TokenManager供其他组件使用
export { TokenManager };