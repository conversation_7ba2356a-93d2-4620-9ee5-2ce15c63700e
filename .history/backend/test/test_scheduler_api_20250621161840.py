"""
调度器API测试脚本
用于测试调度器管理接口是否正常工作
包括单元测试和集成测试
"""

import requests
import json
import time
import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# 添加backend目录到路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# API基础URL
BASE_URL = "http://localhost:5000/api/scheduler"

def test_scheduler_health():
    """测试调度器健康检查"""
    print("=== 测试调度器健康检查 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_scheduler_status():
    """测试获取调度器状态"""
    print("\n=== 测试获取调度器状态 ===")
    try:
        response = requests.get(f"{BASE_URL}/status")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"获取状态失败: {e}")
        return None

def test_scheduler_start():
    """测试启动调度器"""
    print("\n=== 测试启动调度器 ===")
    try:
        response = requests.post(f"{BASE_URL}/start")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"启动调度器失败: {e}")
        return None

def test_scheduler_stop():
    """测试停止调度器"""
    print("\n=== 测试停止调度器 ===")
    try:
        response = requests.post(f"{BASE_URL}/stop")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"停止调度器失败: {e}")
        return None

def test_scheduler_restart():
    """测试重启调度器"""
    print("\n=== 测试重启调度器 ===")
    try:
        response = requests.post(f"{BASE_URL}/restart")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"重启调度器失败: {e}")
        return None

def test_refresh_personnel():
    """测试刷新人员名单"""
    print("\n=== 测试刷新人员名单 ===")
    try:
        response = requests.post(f"{BASE_URL}/refresh")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"刷新人员名单失败: {e}")
        return None

def test_immediate_call():
    """测试立即拨号（使用测试号码）"""
    print("\n=== 测试立即拨号 ===")
    try:
        test_phone = "13800138000"  # 测试号码
        data = {"phone": test_phone}
        response = requests.post(f"{BASE_URL}/call/immediate", json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"立即拨号失败: {e}")
        return None

def run_all_tests():
    """运行所有测试"""
    print("🤖 AI电话机器人调度器API测试开始")
    print("=" * 50)
    
    # 1. 健康检查
    if not test_scheduler_health():
        print("❌ 健康检查失败，停止测试")
        return
    
    # 2. 获取初始状态
    status = test_scheduler_status()
    
    # 3. 刷新人员名单测试
    test_refresh_personnel()
    
    # 4. 立即拨号测试（可选，注释掉避免实际拨号）
    # test_immediate_call()
    
    # 5. 调度器管理测试
    if status and status.get("status") == "stopped":
        print("\n📱 调度器当前已停止，测试启动功能")
        start_result = test_scheduler_start()
        
        if start_result and start_result.get("success"):
            print("✅ 调度器启动成功，等待5秒后查看状态")
            time.sleep(5)
            test_scheduler_status()
            
            print("🔄 测试重启功能")
            test_scheduler_restart()
            
            print("⏹️ 测试停止功能")
            time.sleep(2)
            test_scheduler_stop()
    else:
        print("\n📱 调度器当前正在运行，测试停止和启动功能")
        test_scheduler_stop()
        time.sleep(2)
        test_scheduler_start()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

if __name__ == "__main__":
    run_all_tests() 