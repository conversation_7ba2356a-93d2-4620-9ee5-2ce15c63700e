from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from database import get_db
from auth.auth import require_auth, require_doctor_filter, get_user_patient_filter, get_user_call_records_filter
from utils.serializers import serialize_doc
from bson import ObjectId

stats_bp = Blueprint('stats', __name__, url_prefix='/api')

@stats_bp.route('/call-records', methods=['GET'])
@require_auth
@require_doctor_filter
def get_call_records():
    """获取通话记录列表"""
    try:
        db = get_db()
        search = request.args.get('search', '')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        patient_ids = request.args.get('patient_ids')
        doctor_intervention = request.args.get('doctor_intervention')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))

        # 构建查询条件
        query = {}
        if search:
            query['$or'] = [
                {'患者名字': {'$regex': search, '$options': 'i'}},
                {'手机号': {'$regex': search, '$options': 'i'}},
                {'患者编号': {'$regex': search, '$options': 'i'}}
            ]

        if start_date and end_date:
            query['记录日期'] = {
                '$gte': start_date,
                '$lte': end_date
            }

        # 患者编号筛选（支持多个编号，用逗号分隔）
        if patient_ids and patient_ids.strip():
            patient_id_list = [pid.strip() for pid in patient_ids.split(',') if pid.strip()]
            if patient_id_list:
                query['患者编号'] = {'$in': patient_id_list}
                print(f"🔍 患者编号筛选: {patient_id_list}")  # 调试日志

        # 医生干预筛选
        if doctor_intervention and doctor_intervention != '全部':
            query['是否需要医生人工和患者联系'] = doctor_intervention
            print(f"🔍 医生干预筛选: {doctor_intervention}")  # 调试日志

        # 添加医生权限过滤
        call_filter = get_user_call_records_filter(request.current_user, db)
        query.update(call_filter)

        print(f"🔍 最终查询条件: {query}")  # 调试日志

        # 获取总数
        total = db.call_records.count_documents(query)
        print(f"🔍 查询结果总数: {total}")  # 调试日志

        # 分页查询
        skip = (page - 1) * limit
        cursor = db.call_records.find(query).sort([('创建时间', -1)]).skip(skip).limit(limit)
        records = [serialize_doc(doc) for doc in cursor]

        return jsonify({
            'success': True,
            'data': records,
            'total': total,
            'page': page,
            'limit': limit
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/stats', methods=['GET'])
@require_auth
@require_doctor_filter
def get_stats():
    """获取统计数据"""
    try:
        db = get_db()

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        call_filter = get_user_call_records_filter(request.current_user, db)

        # 获取基本统计数据（按权限过滤）
        total_personnel = db.personnel.count_documents(patient_filter)
        active_personnel_filter = patient_filter.copy()
        active_personnel_filter['training_status'] = '训练中'
        active_personnel = db.personnel.count_documents(active_personnel_filter)

        # 今日相关统计（按权限过滤）
        today = datetime.now().strftime('%Y-%m-%d')
        today_filter = call_filter.copy()
        today_filter['记录日期'] = today
        today_records = list(db.call_records.find(today_filter))

        pending_today = len([r for r in today_records if not r.get('训练完成情况')])
        completed_today = len([r for r in today_records if r.get('训练完成情况') == '完成'])

        # 计算完成率
        completion_rate = (completed_today / max(len(today_records), 1)) * 100 if today_records else 0

        # 计算平均训练次数
        training_counts = []
        for r in today_records:
            if r.get('训练次数') and r.get('训练次数') != '--':
                try:
                    # 提取训练次数的数字部分
                    training_str = str(r['训练次数'])
                    training_num = ''.join(filter(str.isdigit, training_str))
                    if training_num:
                        training_counts.append(int(training_num))
                except (ValueError, TypeError):
                    continue
        avg_training = sum(training_counts) / len(training_counts) if training_counts else 0

        return jsonify({
            'success': True,
            'data': {
                'totalPersonnel': total_personnel,
                'pendingToday': pending_today,
                'completedToday': completed_today,
                'avgTraining': round(avg_training),
                'completionRate': round(completion_rate, 1)
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/progress', methods=['GET'])
@require_auth
@require_doctor_filter
def get_progress():
    """获取康复进度数据"""
    try:
        db = get_db()
        search = request.args.get('search', '')

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)

        # 获取所有患者（按权限过滤）- 修改为获取所有患者而不仅仅是训练中的患者
        query = patient_filter.copy()
        # 移除训练状态限制，为所有患者计算进度数据
        # query['training_status'] = '训练中'
        if search:
            query['name'] = {'$regex': search, '$options': 'i'}

        personnel = list(db.personnel.find(query))

        # 为每个患者计算进度数据
        progress_data = {}
        for person in personnel:
            try:
                # 获取患者的通话记录
                records = list(db.call_records.find({
                    '手机号': person['phone']
                }).sort([('创建时间', 1)]))

                # 计算进度指标
                enrollment_date = datetime.strptime(person['enrollment_date'], '%Y-%m-%d')
                current_day = (datetime.now() - enrollment_date).days + 1
                total_days = 30

                # 计算依从性
                completed_days = len([r for r in records if r.get('训练完成情况') == '完成'])
                compliance = (completed_days / max(current_day, 1)) * 100

                # 今日训练数据
                today = datetime.now().strftime('%Y-%m-%d')
                today_record = next((r for r in records if r.get('记录日期') == today), None)
                today_training = 0
                if today_record and today_record.get('训练次数'):
                    try:
                        # 提取训练次数的数字部分，处理"720次"这种格式
                        training_str = str(today_record['训练次数'])
                        training_num = ''.join(filter(str.isdigit, training_str))
                        if training_num:
                            today_training = int(training_num)
                    except (ValueError, TypeError):
                        today_training = 0

                # 平均训练次数
                training_counts = []
                for r in records:
                    if r.get('训练次数') and r.get('训练次数') != '--':
                        try:
                            # 提取训练次数的数字部分
                            training_str = str(r['训练次数'])
                            training_num = ''.join(filter(str.isdigit, training_str))
                            if training_num:
                                training_counts.append(int(training_num))
                        except (ValueError, TypeError):
                            continue

                avg_training = sum(training_counts) / len(training_counts) if training_counts else 0

                progress_data[str(person['_id'])] = {
                    'currentDay': current_day,  # 移除min限制，显示实际天数
                    'totalDays': total_days,
                    'compliance': round(min(compliance, 100)),
                    'todayTraining': today_training,
                    'avgTraining': round(avg_training)
                }
            except Exception as person_error:
                # 为出错的患者提供默认值
                progress_data[str(person['_id'])] = {
                    'currentDay': 0,
                    'totalDays': 30,
                    'compliance': 0,
                    'todayTraining': 0,
                    'avgTraining': 0
                }

        return jsonify({
            'success': True,
            'data': {
                'personnel': [serialize_doc(doc) for doc in personnel],
                'progress': progress_data
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/follow-up-stats', methods=['GET'])
@require_auth
@require_doctor_filter
def get_follow_up_stats():
    """获取历史随访统计数据"""
    try:
        db = get_db()

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        time_range = request.args.get('time_range', 'day')  # day, week, month, year
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        # 如果没有指定日期范围，默认获取最近30天的数据
        if not start_date or not end_date:
            from datetime import datetime, timedelta
            end_date = datetime.now().strftime('%Y-%m-%d')
            if time_range == 'day':
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            elif time_range == 'week':
                start_date = (datetime.now() - timedelta(weeks=12)).strftime('%Y-%m-%d')  # 最近12周
            elif time_range == 'month':
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')  # 最近12个月
            elif time_range == 'year':
                start_date = (datetime.now() - timedelta(days=365*3)).strftime('%Y-%m-%d')  # 最近3年

        print(f"📊 获取随访统计数据: {start_date} 到 {end_date}, 时间维度: {time_range}")  # 调试日志

        # 添加医生权限过滤
        call_filter = get_user_call_records_filter(request.current_user, db)

        # 获取指定日期范围内的所有通话记录（按权限过滤）
        query = call_filter.copy()
        query['记录日期'] = {
            '$gte': start_date,
            '$lte': end_date
        }

        all_records = list(db.call_records.find(query))
        print(f"📊 找到通话记录总数: {len(all_records)}")  # 调试日志
        
        # 时间分组函数
        def get_time_group_key(date_str, time_range):
            """根据时间维度获取分组键"""
            try:
                from datetime import datetime, timedelta
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                
                if time_range == 'day':
                    return date_str  # 按天：使用原日期
                elif time_range == 'week':
                    # 按周：获取该日期所在周的周一日期作为键
                    days_since_monday = date_obj.weekday()  # 0=周一, 6=周日
                    monday = date_obj - timedelta(days=days_since_monday)
                    monday_str = monday.strftime('%Y-%m-%d')
                    print(f"🔍 周分组: {date_str} -> 周一键: {monday_str}")
                    return monday_str
                elif time_range == 'month':
                    # 按月：使用年-月格式
                    return date_obj.strftime('%Y-%m')
                elif time_range == 'year':
                    # 按年：使用年份
                    return date_obj.strftime('%Y')
                else:
                    return date_str
            except Exception as e:
                print(f"❌ 时间分组键生成失败: {date_str}, 错误: {e}")
                return date_str
        
        # 按时间维度分组统计
        stats_by_time = {}
        
        # print(f"🔍 开始分组统计，时间范围: {time_range}")  # 调试日志
        
        for record in all_records:
            record_date = record.get('记录日期', '')
            if not record_date:
                continue
            
            # 获取时间分组键
            time_key = get_time_group_key(record_date, time_range)
            # print(f"📅 原日期: {record_date} -> 分组键: {time_key}")  # 调试日志
            
            if time_key not in stats_by_time:
                stats_by_time[time_key] = {
                    'time_key': time_key,
                    'total_follow_up_people': set(),  # 使用set避免重复统计同一患者
                    'total_calls': 0,
                    'connected_people': set(),  # 接通人数（按手机号去重）
                    'training_people': set(),  # 有训练的人数（按手机号去重）
                    'doctor_intervention_people': set()  # 需要医生干预的人数
                }
            
            # 统计总随访人数（按手机号去重）
            phone = record.get('手机号', '')
            if phone:
                stats_by_time[time_key]['total_follow_up_people'].add(phone)
            
            # 统计电话拨打次数
            stats_by_time[time_key]['total_calls'] += 1
            
            # 统计接通人数（拨号状态为"通话结束"或"通话结束-信息解析完成"）
            dial_status = record.get('拨号状态', '')
            if dial_status in ['通话结束', '通话结束-信息解析完成']:
                if phone:
                    stats_by_time[time_key]['connected_people'].add(phone)
            
            # 统计有训练的人数（按手机号去重）
            training_count = record.get('训练次数', '')
            if training_count and training_count != '--' and training_count != '':
                try:
                    # 提取训练次数的数字部分，如果有训练记录就算作有训练的人
                    training_str = str(training_count)
                    training_num = ''.join(filter(str.isdigit, training_str))
                    if training_num and int(training_num) > 0 and phone:
                        stats_by_time[time_key]['training_people'].add(phone)
                except (ValueError, TypeError):
                    pass
            
            # 统计需要医生干预人数
            need_intervention = record.get('是否需要医生人工和患者联系', '')
            if need_intervention == '是' and phone:
                stats_by_time[time_key]['doctor_intervention_people'].add(phone)
        
        # 转换为最终统计结果
        # print(f"📊 分组结果: {list(stats_by_time.keys())}")  # 调试日志
        # print(f"📊 总分组数: {len(stats_by_time)}")  # 调试日志
        # for key, value in stats_by_time.items():
        #     print(f"📊 分组 {key}: 总随访人数={len(value['total_follow_up_people'])}, 总通话={value['total_calls']}")  # 调试日志
        
        final_stats = []
        for time_key, data in stats_by_time.items():
            # 计算有训练的人数
            training_people_count = len(data.get('training_people', set()))
            
            # 格式化时间显示
            time_display = time_key
            if time_range == 'week':
                # 对于周，显示周一日期和周数
                try:
                    from datetime import datetime
                    monday = datetime.strptime(time_key, '%Y-%m-%d')
                    week_num = monday.isocalendar()[1]
                    time_display = f"{time_key} (第{week_num}周)"
                except Exception as e:
                    print(f"❌ 周时间显示格式化失败: {time_key}, 错误: {e}")
                    time_display = time_key
            elif time_range == 'month':
                # 对于月，显示为 "2024年1月"
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(time_key + '-01', '%Y-%m-%d')
                    time_display = f"{date_obj.year}年{date_obj.month}月"
                except:
                    time_display = time_key
            elif time_range == 'year':
                # 对于年，显示为 "2024年"
                time_display = f"{time_key}年"
            
            final_stats.append({
                'time': time_display,
                'time_key': time_key,  # 用于排序
                'total_follow_up_people': len(data['total_follow_up_people']),
                'total_calls': data['total_calls'],
                'connected_people': len(data['connected_people']),
                'training_people_count': training_people_count,
                'doctor_intervention_people': len(data['doctor_intervention_people'])
            })
        
        # 按时间倒序排列（最近的在前面）
        final_stats.sort(key=lambda x: x['time_key'], reverse=True)
        
        # 分页处理
        total_count = len(final_stats)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_stats = final_stats[start_index:end_index]
        
        print(f"📊 统计结果: 总天数={total_count}, 当前页数据={len(paginated_stats)}")  # 调试日志
        
        return jsonify({
            'success': True,
            'data': paginated_stats,
            'total': total_count,
            'page': page,
            'limit': limit,
            'time_range': time_range
        })
        
    except Exception as e:
        print(f"💥 get_follow_up_stats异常: {str(e)}")  # 调试日志
        import traceback
        print(f"💥 异常详情: {traceback.format_exc()}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/insights', methods=['GET'])
@require_auth
def get_insights():
    """获取智能摘要数据"""
    try:
        from mock_data import get_mock_insights_data
        
        time_range = request.args.get('time_range', '最近7天')
        
        # 使用mock数据作为示例
        mock_insights = get_mock_insights_data()
        
        return jsonify({
            'success': True,
            'data': mock_insights
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'success': True,
        'message': 'API服务正常运行',
        'timestamp': datetime.utcnow().isoformat()
    })

# 以下是新增的拨打电话相关API接口
import sys 

AI_CALL_PATH = r'D:\57_project_codes\phoneAI\AICall\SuiFangMedCall'
if AI_CALL_PATH not in sys.path:
    sys.path.append(AI_CALL_PATH)

# 导入make_immediate_call函数
try:
    from scheduler_manager import make_immediate_call
    SCHEDULER_AVAILABLE = True
except ImportError as e:
    SCHEDULER_AVAILABLE = False

@stats_bp.route('/call-records/immediate-call', methods=['POST'])
@require_auth
def api_make_immediate_call():
    """发起即时通话"""
    try:
        print("🔍 接收到拨号请求")  # 调试日志
        
        if not SCHEDULER_AVAILABLE:
            print("❌ 调度器模块不可用")  # 调试日志
            return jsonify({
                'success': False,
                'error': '调度器模块不可用，请检查AICall工程目录是否正确配置'
            }), 503
        
        data = request.get_json()
        print(f"📦 请求数据: {data}")  # 调试日志
        
        if not data:
            print("❌ 请求数据格式错误")  # 调试日志
            return jsonify({
                'success': False,
                'error': '请求数据格式错误'
            }), 400
        
        phone_number = data.get('phone_number')
        call_id = data.get('obj_id')  # 前端仍然发送obj_id，但我们在后端用call_id变量名
        
        print(f"📞 电话号码: {phone_number}, 通话ID: {call_id}")  # 调试日志
        
        if not phone_number:
            print("❌ 缺少phone_number参数")  # 调试日志
            return jsonify({
                'success': False,
                'error': '缺少phone_number参数'
            }), 400
        
        # 简单的手机号格式验证
        if not isinstance(phone_number, str) or len(phone_number.strip()) == 0:
            print("❌ 手机号格式不正确")  # 调试日志
            return jsonify({
                'success': False,
                'error': '手机号格式不正确'
            }), 400
        
        print("🚀 准备调用make_immediate_call函数")  # 调试日志
        
        # 调用make_immediate_call函数
        if call_id:
            print(f"📞 调用make_immediate_call({phone_number.strip()}, obj_id={call_id})")  # 调试日志
            result = make_immediate_call(phone_number.strip(), obj_id=call_id)
        else:
            print(f"📞 调用make_immediate_call({phone_number.strip()})")  # 调试日志
            result = make_immediate_call(phone_number.strip())
        
        print(f"📋 make_immediate_call返回结果: {result}")  # 调试日志
        
        if result.get('success'):
            print("✅ 通话开始成功")  # 调试日志
            return jsonify({
                'success': True,
                'message': '通话已开始',
                'call_id': call_id,
                'data': result
            })
        else:
            print(f"❌ 通话开始失败: {result.get('message', '拨打电话失败')}")  # 调试日志
            return jsonify({
                'success': False,
                'error': result.get('message', '拨打电话失败')
            }), 400
        
    except Exception as e:
        print(f"💥 API函数异常: {str(e)}")  # 调试日志
        import traceback
        print(f"💥 异常详情: {traceback.format_exc()}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/call-records/status/<call_id>', methods=['GET'])
@require_auth
def get_call_status(call_id):
    """根据call_id获取通话状态"""
    try:
        print(f"🔍 查找通话记录，call_id: {call_id}")  # 调试日志
        
        db = get_db()
        
        # 尝试多种可能的字段来查找通话记录
        call_record = None
        
        call_record = db.call_records.find_one({'_id': call_id})
        
        # 4. 如果还没找到，查看数据库中最新的记录结构，用于调试
        # if not call_record:
        #     latest_record = db.call_records.find_one({}, sort=[('创建时间', -1)])
        #     if latest_record:
        #         print(f"📋 最新通话记录的字段: {list(latest_record.keys())}")  # 调试日志
        #         print(f"📋 最新通话记录内容: {latest_record}")  # 调试日志
        #     else:
        #         print("📋 数据库中没有通话记录")  # 调试日志
        
        if not call_record:
            print(f"❌ 未找到对应的通话记录: {call_id}")  # 调试日志
            return jsonify({
                'success': False,
                'error': '未找到对应的通话记录'
            }), 404
        
        print(f"✅ 找到通话记录: {call_record.get('_id')}")  # 调试日志
        
        # 返回通话状态信息
        return jsonify({
            'success': True,
            'data': {
                'call_id': call_id,
                '拨号状态': call_record.get('拨号状态', '未知'),
                '记录日期': call_record.get('记录日期'),
                '通话时间': call_record.get('通话时间'),
                '创建时间': call_record.get('创建时间'),
                '手机号': call_record.get('手机号')
            }
        })
        
    except Exception as e:
        print(f"💥 get_call_status异常: {str(e)}")  # 调试日志
        import traceback
        print(f"💥 异常详情: {traceback.format_exc()}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/call-records/detail/<call_id>', methods=['GET'])
@require_auth
def get_call_detail(call_id):
    """根据call_id获取通话详情"""
    try:
        print(f"🔍 查找通话详情，call_id: {call_id}")  # 调试日志
        
        db = get_db()
        
        # 尝试多种可能的字段来查找通话记录
        call_record = None
        
        call_record = db.call_records.find_one({'_id': call_id})
        
        
        if not call_record:
            print(f"❌ 未找到对应的通话详情: {call_id}")  # 调试日志
            return jsonify({
                'success': False,
                'error': '未找到对应的通话记录'
            }), 404
        
        print(f"✅ 找到通话详情: {call_record.get('_id')}")  # 调试日志
        
        # 返回完整的通话记录信息
        return jsonify({
            'success': True,
            'data': serialize_doc(call_record)
        })
        
    except Exception as e:
        print(f"💥 get_call_detail异常: {str(e)}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/call-records/<record_id>', methods=['DELETE'])
@require_auth
def delete_call_record(record_id):
    """删除单个通话记录"""
    try:
        print(f"🗑️ 删除通话记录请求，record_id: {record_id}")  # 调试日志
        
        db = get_db()
        
        # 尝试多种方式查找记录
        existing_record = None
        query_used = None
        
        # 1. 先尝试作为字符串直接查询
        try:
            existing_record = db.call_records.find_one({'_id': record_id})
            if existing_record:
                query_used = {'_id': record_id}
                print(f"✅ 通过字符串ID找到记录: {record_id}")  # 调试日志
        except Exception as e:
            print(f"🔍 字符串ID查询失败: {e}")  # 调试日志
        
        # 2. 如果没找到，尝试转换为ObjectId查询
        if not existing_record:
            try:
                object_id = ObjectId(record_id)
                existing_record = db.call_records.find_one({'_id': object_id})
                if existing_record:
                    query_used = {'_id': object_id}
                    print(f"✅ 通过ObjectId找到记录: {record_id}")  # 调试日志
            except Exception as e:
                print(f"🔍 ObjectId查询失败: {e}")  # 调试日志
        
        if not existing_record:
            print(f"❌ 所有方式都未找到要删除的记录: {record_id}")  # 调试日志
            return jsonify({
                'success': False,
                'error': '未找到要删除的记录'
            }), 404
        
        # 使用成功的查询条件删除记录
        result = db.call_records.delete_one(query_used)
        
        if result.deleted_count > 0:
            print(f"✅ 成功删除记录: {record_id}")  # 调试日志
            return jsonify({
                'success': True,
                'message': '记录删除成功',
                'deleted_id': record_id
            })
        else:
            print(f"❌ 删除失败: {record_id}")  # 调试日志
            return jsonify({
                'success': False,
                'error': '删除记录失败'
            }), 500
        
    except Exception as e:
        print(f"💥 delete_call_record异常: {str(e)}")  # 调试日志
        import traceback
        print(f"💥 异常详情: {traceback.format_exc()}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@stats_bp.route('/call-records/batch', methods=['DELETE'])
@require_auth
def batch_delete_call_records():
    """批量删除通话记录"""
    try:
        print("🗑️ 批量删除通话记录请求")  # 调试日志
        
        data = request.get_json()
        if not data:
            print("❌ 请求数据为空")  # 调试日志
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        # 兼容两种参数名称：ids 和 record_ids
        record_ids = data.get('ids', data.get('record_ids', []))
        
        if not isinstance(record_ids, list) or len(record_ids) == 0:
            print("❌ 记录ID参数格式错误或为空")  # 调试日志
            return jsonify({
                'success': False,
                'error': '记录ID参数必须是非空数组'
            }), 400
        
        print(f"📋 要删除的记录ID列表: {record_ids}")  # 调试日志
        
        db = get_db()
        
        # 分别收集可以删除的记录ID（按不同格式）
        string_ids_to_delete = []  # 字符串格式的ID
        object_ids_to_delete = []  # ObjectId格式的ID
        found_records = []
        
        for record_id in record_ids:
            found = False
            
            # 1. 先尝试作为字符串查询
            try:
                existing_record = db.call_records.find_one({'_id': record_id}, {'_id': 1})
                if existing_record:
                    string_ids_to_delete.append(record_id)
                    found_records.append(record_id)
                    found = True
                    print(f"✅ 通过字符串ID找到记录: {record_id}")  # 调试日志
            except Exception as e:
                print(f"🔍 字符串ID查询失败 {record_id}: {e}")  # 调试日志
            
            # 2. 如果没找到，尝试转换为ObjectId查询
            if not found:
                try:
                    object_id = ObjectId(record_id)
                    existing_record = db.call_records.find_one({'_id': object_id}, {'_id': 1})
                    if existing_record:
                        object_ids_to_delete.append(object_id)
                        found_records.append(record_id)
                        found = True
                        print(f"✅ 通过ObjectId找到记录: {record_id}")  # 调试日志
                except Exception as e:
                    print(f"🔍 ObjectId查询失败 {record_id}: {e}")  # 调试日志
            
            if not found:
                print(f"❌ 未找到记录: {record_id}")  # 调试日志
        
        print(f"📋 找到的记录ID: {found_records}")  # 调试日志
        print(f"📋 字符串格式ID: {string_ids_to_delete}")  # 调试日志
        print(f"📋 ObjectId格式ID: {len(object_ids_to_delete)}个")  # 调试日志
        
        # 分别执行删除操作
        total_deleted = 0
        
        # 删除字符串格式的记录
        if string_ids_to_delete:
            result1 = db.call_records.delete_many({'_id': {'$in': string_ids_to_delete}})
            total_deleted += result1.deleted_count
            print(f"📋 字符串ID删除结果: {result1.deleted_count}")  # 调试日志
        
        # 删除ObjectId格式的记录
        if object_ids_to_delete:
            result2 = db.call_records.delete_many({'_id': {'$in': object_ids_to_delete}})
            total_deleted += result2.deleted_count
            print(f"📋 ObjectId删除结果: {result2.deleted_count}")  # 调试日志
        
        deleted_count = total_deleted
        not_found_count = len(record_ids) - deleted_count
        
        print(f"✅ 删除结果: 成功删除 {deleted_count} 条，未找到 {not_found_count} 条")  # 调试日志
        
        return jsonify({
            'success': True,
            'message': f'批量删除完成：成功删除 {deleted_count} 条记录',
            'deleted_count': deleted_count,
            'not_found_count': not_found_count,
            'deleted_ids': found_records
        })
        
    except Exception as e:
        print(f"💥 batch_delete_call_records异常: {str(e)}")  # 调试日志
        import traceback
        print(f"💥 异常详情: {traceback.format_exc()}")  # 调试日志
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 