#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== 停止所有服务 ==="
echo ""

# 停止后端服务
echo "1. 停止后端服务..."
if [ -f "$SCRIPT_DIR/backend.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/backend.pid")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "   正在停止后端服务 (PID: $PID)..."
        kill "$PID"
        sleep 3
        # 检查进程是否还在运行
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "   强制停止后端服务..."
            kill -9 "$PID"
            sleep 1
        fi
        rm -f "$SCRIPT_DIR/backend.pid"
        echo "   后端服务已停止"
    else
        echo "   后端服务进程不存在"
        rm -f "$SCRIPT_DIR/backend.pid"
    fi
else
    echo "   后端服务未运行"
fi

echo ""

# 停止前端服务
echo "2. 停止前端服务..."
if [ -f "$SCRIPT_DIR/frontend.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/frontend.pid")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "   正在停止前端服务 (PID: $PID)..."
        kill "$PID"
        sleep 3
        # 检查进程是否还在运行
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "   强制停止前端服务..."
            kill -9 "$PID"
            sleep 1
        fi
        rm -f "$SCRIPT_DIR/frontend.pid"
        echo "   前端服务已停止"
    else
        echo "   前端服务进程不存在"
        rm -f "$SCRIPT_DIR/frontend.pid"
    fi
else
    echo "   前端服务未运行"
fi

echo ""

# 停止frpc服务
# echo "3. 停止frpc服务..."
# if [ -f "$SCRIPT_DIR/frpc.pid" ]; then
#     PID=$(cat "$SCRIPT_DIR/frpc.pid")
#     if ps -p "$PID" > /dev/null 2>&1; then
#         echo "   正在停止frpc服务 (PID: $PID)..."
#         kill "$PID"
#         sleep 3
#         # 检查进程是否还在运行
#         if ps -p "$PID" > /dev/null 2>&1; then
#             echo "   强制停止frpc服务..."
#             kill -9 "$PID"
#             sleep 1
#         fi
#         rm -f "$SCRIPT_DIR/frpc.pid"
#         echo "   frpc服务已停止"
#     else
#         echo "   frpc服务进程不存在"
#         rm -f "$SCRIPT_DIR/frpc.pid"
#     fi
# else
#     echo "   frpc服务未运行"
# fi

echo ""

# 额外检查：通过端口kill进程（备用方案）
echo "4. 检查端口占用情况..."

# 检查并kill 5000端口的进程
PORT_5000_PID=$(lsof -t -i:5000 2>/dev/null)
if [ -n "$PORT_5000_PID" ]; then
    echo "   发现端口5000仍被占用 (PID: $PORT_5000_PID)，强制结束..."
    kill -9 $PORT_5000_PID 2>/dev/null
fi

# 检查并kill 5173端口的进程
PORT_5173_PID=$(lsof -t -i:5173 2>/dev/null)
if [ -n "$PORT_5173_PID" ]; then
    echo "   发现端口5173仍被占用 (PID: $PORT_5173_PID)，强制结束..."
    kill -9 $PORT_5173_PID 2>/dev/null
fi

echo ""

# 清理相关的Python进程（可选，谨慎使用）
echo "5. 清理相关进程..."
# 查找可能遗留的Python进程
PYTHON_PIDS=$(pgrep -f "smart_start.py\|server.py" 2>/dev/null)
if [ -n "$PYTHON_PIDS" ]; then
    echo "   发现相关Python进程: $PYTHON_PIDS"
    echo "   是否强制结束这些进程? (y/N)"
    read -t 10 -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo $PYTHON_PIDS | xargs kill -9 2>/dev/null
        echo "   已强制结束相关Python进程"
    else
        echo "   跳过清理Python进程"
    fi
else
    echo "   未发现相关Python进程"
fi

echo ""
echo "=== 所有服务已停止 ==="

# 最终状态检查
echo ""
echo "最终状态检查:"
echo "  端口5000: $(ss -tlnp | grep :5000 | wc -l)个进程占用"
echo "  端口5173: $(ss -tlnp | grep :5173 | wc -l)个进程占用"
echo "  PID文件: $(ls -1 $SCRIPT_DIR/*.pid 2>/dev/null | wc -l)个"

# 清理所有PID文件
rm -f "$SCRIPT_DIR"/*.pid 2>/dev/null
echo "  已清理所有PID文件"