#!/bin/bash
# 初始化conda环境
eval "$(conda shell.bash hook)"
# 或者使用
# source ~/anaconda3/etc/profile.d/conda.sh  # 如果是anaconda
# source ~/miniconda3/etc/profile.d/conda.sh  # 如果是miniconda

# 激活conda环境
conda activate py10
# 脚本所在目录 (frpc目录)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 后端项目目录
BACKEND_DIR="/home/<USER>/workspace/suifangweb/backend"
# 前端项目目录
FRONTEND_DIR="/home/<USER>/workspace/suifangweb"

# 日志文件最大大小 (MB)
MAX_LOG_SIZE=10
# 保留的日志文件数量
KEEP_LOGS=3

# 日志轮转函数
rotate_log() {
    local log_file="$1"
    local max_size_mb="$2"
    local keep_count="$3"

    if [ -f "$log_file" ]; then
        # 获取文件大小(MB)
        local size_mb=$(du -m "$log_file" | cut -f1)

        if [ "$size_mb" -gt "$max_size_mb" ]; then
            echo "日志文件 $log_file 大小超过 ${max_size_mb}MB，进行轮转..."

            # 删除最旧的日志
            for i in $(seq $((keep_count-1)) -1 1); do
                if [ -f "${log_file}.$i" ]; then
                    mv "${log_file}.$i" "${log_file}.$((i+1))"
                fi
            done

            # 备份当前日志
            mv "$log_file" "${log_file}.1"

            # 创建新的空日志文件
            touch "$log_file"
        fi
    fi
}

echo "=== 启动所有服务 (带日志轮转) ==="
echo ""

# 检查并轮转现有日志
rotate_log "$SCRIPT_DIR/backend.log" $MAX_LOG_SIZE $KEEP_LOGS
rotate_log "$SCRIPT_DIR/frontend.log" $MAX_LOG_SIZE $KEEP_LOGS
rotate_log "$SCRIPT_DIR/frpc.log" $MAX_LOG_SIZE $KEEP_LOGS

# 检查目录是否存在
if [ ! -d "$BACKEND_DIR" ]; then
    echo "错误: 后端目录不存在: $BACKEND_DIR"
    exit 1
fi

if [ ! -d "$FRONTEND_DIR" ]; then
    echo "错误: 前端目录不存在: $FRONTEND_DIR"
    exit 1
fi

# 1. 启动后端服务
echo "1. 启动后端服务 (smart_start.py)..."
cd "$BACKEND_DIR"
nohup python smart_start.py > "$SCRIPT_DIR/backend.log" 2>&1 &
echo $! > "$SCRIPT_DIR/backend.pid"
echo "   后端服务已启动 (PID: $(cat $SCRIPT_DIR/backend.pid))"
sleep 2

echo ""

# 2. 启动前端服务 (限制日志输出)
echo "2. 启动前端服务 (server.py)..."
cd "$FRONTEND_DIR"
# 使用rotatelogs或直接限制输出
nohup python server.py 2>&1 | head -c 50M > "$SCRIPT_DIR/frontend.log" &
echo $! > "$SCRIPT_DIR/frontend.pid"
echo "   前端服务已启动 (PID: $(cat $SCRIPT_DIR/frontend.pid))"
sleep 2

echo ""

# 3. 启动frpc客户端
# echo "3. 启动frpc客户端..."
# cd "$SCRIPT_DIR"
# nohup ./frpc -c ./frpc.toml > "$SCRIPT_DIR/frpc.log" 2>&1 &
# echo $! > "$SCRIPT_DIR/frpc.pid"
# echo "   frpc已启动 (PID: $(cat $SCRIPT_DIR/frpc.pid))"

echo ""
echo "=== 所有服务启动完成 ==="
echo "日志配置: 最大${MAX_LOG_SIZE}MB, 保留${KEEP_LOGS}个历史文件"