# API 配置指南

## 概述

前端应用现在支持通过 `vite.config.ts` 中的环境变量来配置后端 API 服务器地址，而不是硬编码在代码中。

## 配置方式

### 1. 通过环境变量配置（推荐）

创建 `.env` 文件（参考 `.env.example`）：

```bash
# API服务器配置
VITE_API_HOST=***************
VITE_API_PORT=5000
```

### 2. 直接修改 vite.config.ts

在 `vite.config.ts` 文件中修改：

```typescript
// 后端API服务器配置
const API_HOST = env.VITE_API_HOST || '***************'
const API_PORT = env.VITE_API_PORT || '5000'
```

## 修改的文件

### 1. `src/services/api.ts`
- 修改 `getApiBaseUrl()` 函数使用全局变量 `__API_BASE_URL__`
- 不再硬编码 API 地址

### 2. `src/services/schedulerService.ts`
- 修改构造函数使用全局变量 `__API_HOST__` 和 `__API_PORT__`
- 动态构建调度器服务的 URL

### 3. `src/vite-env.d.ts`
- 添加全局变量的 TypeScript 类型声明

### 4. `vite.config.ts`
- 通过 `define` 配置将环境变量注入为全局变量

## 使用方法

### 开发环境

1. 复制 `.env.example` 为 `.env`
2. 修改 `.env` 中的 `VITE_API_HOST` 和 `VITE_API_PORT`
3. 重新启动开发服务器：`npm run dev`

### 生产环境

1. 设置环境变量或修改 `vite.config.ts`
2. 重新构建：`npm run build`
3. 部署 `dist` 目录

## 示例配置

### 本地开发
```bash
VITE_API_HOST=127.0.0.1
VITE_API_PORT=5000
```

### 生产服务器
```bash
VITE_API_HOST=***************
VITE_API_PORT=5000
```

### 其他服务器
```bash
VITE_API_HOST=*************
VITE_API_PORT=8000
```

## 注意事项

1. 修改配置后需要重新构建前端应用
2. 环境变量必须以 `VITE_` 开头才能被 Vite 识别
3. 如果没有设置环境变量，会使用默认值 `***************:5000`
